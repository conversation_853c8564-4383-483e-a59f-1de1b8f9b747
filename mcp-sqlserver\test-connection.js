import sql from 'mssql';

async function testConnection() {
  try {
    const config = {
      server: '14.103.246.164',
      database: 'StudentsCMSSP',
      user: 'StudentsCMSSP',
      password: 'Xg2LS44Cyz5Zt8.',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
      },
      pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
      }
    };

    console.log('尝试连接数据库...');
    console.log('服务器:', config.server);
    console.log('数据库:', config.database);
    console.log('用户:', config.user);

    const pool = await sql.connect(config);
    console.log('数据库连接成功！');

    // 测试简单查询
    const result = await pool.request().query('SELECT @@VERSION as version');
    console.log('SQL Server版本:', result.recordset[0].version);

    // 列出表
    const tables = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    console.log(`找到 ${tables.recordset.length} 个表:`);
    tables.recordset.forEach(table => {
      console.log('  -', table.TABLE_NAME);
    });

    await sql.close();
    console.log('连接已关闭');

  } catch (error) {
    console.error('数据库连接失败:', error.message);
    console.error('错误详情:', error);
  }
}

testConnection();
