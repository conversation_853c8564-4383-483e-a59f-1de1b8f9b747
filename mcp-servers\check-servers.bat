@echo off
echo 检查StudentsCMSSP MCP服务器状态...
echo.

echo 1. 检查SQL Server MCP服务器...
if exist "..\mcp-sqlserver\server.js" (
    echo   ✓ SQL Server MCP服务器文件存在
) else (
    echo   ✗ SQL Server MCP服务器文件不存在
)

echo.
echo 2. 检查Filesystem MCP服务器...
if exist "filesystem-mcp\server.js" (
    echo   ✓ Filesystem MCP服务器文件存在
) else (
    echo   ✗ Filesystem MCP服务器文件不存在
)

echo.
echo 3. 检查Excel MCP服务器...
if exist "excel-mcp\server.js" (
    echo   ✓ Excel MCP服务器文件存在
) else (
    echo   ✗ Excel MCP服务器文件不存在
)

echo.
echo 4. 检查Pandoc MCP服务器...
if exist "pandoc-mcp\server.js" (
    echo   ✓ Pandoc MCP服务器文件存在
) else (
    echo   ✗ Pandoc MCP服务器文件不存在
)

echo.
echo 5. 检查依赖安装...
if exist "filesystem-mcp\node_modules" (
    echo   ✓ Filesystem MCP依赖已安装
) else (
    echo   ✗ Filesystem MCP依赖未安装
)

if exist "excel-mcp\node_modules" (
    echo   ✓ Excel MCP依赖已安装
) else (
    echo   ✗ Excel MCP依赖未安装
)

if exist "pandoc-mcp\node_modules" (
    echo   ✓ Pandoc MCP依赖已安装
) else (
    echo   ✗ Pandoc MCP依赖未安装
)

echo.
echo 检查完成！
pause
