#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import sql from 'mssql';

class SQLServerMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'mcp-sqlserver',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'query_database',
            description: 'Execute SQL queries on SQL Server database (SELECT statements only for safety)',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'SQL SELECT query to execute'
                }
              },
              required: ['query']
            }
          },
          {
            name: 'list_tables',
            description: 'List all tables in the database',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          },
          {
            name: 'describe_table',
            description: 'Get the schema/structure of a specific table',
            inputSchema: {
              type: 'object',
              properties: {
                table_name: {
                  type: 'string',
                  description: 'Name of the table to describe'
                }
              },
              required: ['table_name']
            }
          },
          {
            name: 'get_table_data',
            description: 'Get sample data from a table (limited to 100 rows)',
            inputSchema: {
              type: 'object',
              properties: {
                table_name: {
                  type: 'string',
                  description: 'Name of the table to query'
                },
                limit: {
                  type: 'number',
                  description: 'Number of rows to return (max 100)',
                  default: 10
                }
              },
              required: ['table_name']
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        const config = {
          server: process.env.MSSQL_SERVER || '**************',
          database: process.env.MSSQL_DATABASE || 'StudentsCMSSP',
          user: process.env.MSSQL_USER || 'StudentsCMSSP',
          password: process.env.MSSQL_PASSWORD || 'Xg2LS44Cyz5Zt8.',
          options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
          },
          pool: {
            max: 10,
            min: 0,
            idleTimeoutMillis: 30000
          }
        };

        const pool = await sql.connect(config);

        switch (name) {
          case 'query_database':
            // 安全检查：只允许 SELECT 语句
            const query = args.query.trim().toLowerCase();
            if (!query.startsWith('select')) {
              throw new Error('Only SELECT queries are allowed for safety');
            }
            
            const result = await pool.request().query(args.query);
            return {
              content: [
                {
                  type: 'text',
                  text: `Query executed successfully. Found ${result.recordset.length} rows.\n\n` +
                        JSON.stringify(result.recordset, null, 2)
                }
              ]
            };

          case 'list_tables':
            const tables = await pool.request().query(`
              SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
              FROM INFORMATION_SCHEMA.TABLES 
              WHERE TABLE_TYPE = 'BASE TABLE'
              ORDER BY TABLE_SCHEMA, TABLE_NAME
            `);
            return {
              content: [
                {
                  type: 'text',
                  text: `Found ${tables.recordset.length} tables:\n\n` +
                        JSON.stringify(tables.recordset, null, 2)
                }
              ]
            };

          case 'describe_table':
            const columns = await pool.request()
              .input('tableName', sql.NVarChar, args.table_name)
              .query(`
                SELECT 
                  COLUMN_NAME,
                  DATA_TYPE,
                  IS_NULLABLE,
                  COLUMN_DEFAULT,
                  CHARACTER_MAXIMUM_LENGTH,
                  NUMERIC_PRECISION,
                  NUMERIC_SCALE
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = @tableName
                ORDER BY ORDINAL_POSITION
              `);
            return {
              content: [
                {
                  type: 'text',
                  text: `Table structure for '${args.table_name}':\n\n` +
                        JSON.stringify(columns.recordset, null, 2)
                }
              ]
            };

          case 'get_table_data':
            const limit = Math.min(args.limit || 10, 100); // 最大100行
            const data = await pool.request()
              .input('tableName', sql.NVarChar, args.table_name)
              .query(`SELECT TOP (${limit}) * FROM [${args.table_name}]`);
            return {
              content: [
                {
                  type: 'text',
                  text: `Sample data from '${args.table_name}' (${data.recordset.length} rows):\n\n` +
                        JSON.stringify(data.recordset, null, 2)
                }
              ]
            };

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`
            }
          ],
          isError: true
        };
      } finally {
        try {
          await sql.close();
        } catch (e) {
          // 忽略关闭连接时的错误
        }
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('SQL Server MCP server running on stdio');
  }
}

const server = new SQLServerMCPServer();
server.run().catch(console.error);
