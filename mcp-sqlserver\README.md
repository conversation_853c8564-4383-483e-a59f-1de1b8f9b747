# StudentsCMSSP SQL Server MCP服务器

## 🎯 目的
为StudentsCMSSP项目提供AI辅助开发支持，让AI能够：
- 查询数据库结构和数据
- 理解项目架构
- 辅助代码开发和调试
- 提供数据库相关的开发支持

## 🚀 快速启动

### 方法1：使用启动脚本
```bash
start-mcp.bat
```

### 方法2：手动启动
```bash
cd mcp-sqlserver
node server.js
```

### 方法3：使用环境变量
```bash
set MSSQL_SERVER=**************
set MSSQL_DATABASE=StudentsCMSSP
set MSSQL_USER=StudentsCMSSP
set MSSQL_PASSWORD=Xg2LS44Cyz5Zt8.
node server.js
```

## 🛠️ 可用工具

1. **query_database** - 执行SQL查询（仅限SELECT）
2. **list_tables** - 列出所有数据库表
3. **describe_table** - 获取表结构
4. **get_table_data** - 获取表数据样本

## 📊 数据库信息

- **服务器**: **************
- **数据库**: StudentsCMSSP
- **表数量**: 98个表
- **主要模块**: 财务管理、供应链管理、食材溯源、日常管理

## 🔧 配置说明

MCP服务器配置文件：`server.js`
环境变量配置：`.env`

## 📝 使用示例

```javascript
// 查询所有表
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'

// 查看用户表结构
SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users'

// 获取财务凭证数据
SELECT TOP 10 * FROM financial_vouchers ORDER BY created_at DESC
```

## 🎯 AI开发支持

此MCP服务器专门为AI辅助开发StudentsCMSSP项目而设计，提供：
- 安全的数据库只读访问
- 结构化的数据查询接口
- 项目架构理解支持
- 开发调试辅助功能
