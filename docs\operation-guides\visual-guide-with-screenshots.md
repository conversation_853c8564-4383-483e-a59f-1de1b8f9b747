# 智慧食堂平台图文操作指南（含实际截图）

## 📖 指南概述

本指南通过实际截图和详细说明，为您提供智慧食堂平台的完整操作指导。所有截图均来自真实的系统界面，确保指导的准确性和实用性。

---

## 🏠 第一部分：系统访问与登录

### 1.1 访问系统首页

访问官方网址：[xiaoyuanst.com](http://xiaoyuanst.com)

**首页界面展示：**

![智慧食堂平台首页](../screenshots/homepage_overview.png)

**首页核心功能区域：**

🔝 **顶部导航栏**
- 首页：系统介绍和概览
- 核心功能：功能模块详细介绍
- 使用指南：操作帮助文档
- 视频教学：视频教程资源
- 登录：用户登录入口
- 体验系统：游客免费体验

📋 **功能介绍区域**
- 财务管理：专业的财务核算解决方案
- 日常管理：食堂日常运营管理
- 供应链管理：从采购到出库的全流程管理
- 食品安全追溯：完整的食品安全追踪体系
- 系统设置：用户权限和系统配置
- 技术支持：专业的技术服务保障

📖 **新用户入门指南**
- 7个简单步骤快速上手
- 标准化操作流程指导
- 移动端使用说明

💰 **财务管理特色**
- 专业会计科目体系
- 标准复式记账功能
- 完整财务报表生成

📞 **联系我们**
- 技术支持热线：18373062333
- 邮件支持：<EMAIL>
- 服务时间：工作日 9:00-18:00

### 1.2 游客体验登录

**操作步骤：**
1. 点击首页右上角的"体验系统"按钮
2. 系统自动为您分配游客账号
3. 无需注册，直接体验完整功能

**登录优势：**
- ✅ 零门槛体验：无需注册即可使用
- 🔍 完整功能：体验所有核心功能
- 🛡️ 安全隔离：独立的体验环境
- 📱 移动适配：支持手机和平板访问

### 1.3 优雅登录界面

**登录页面展示：**

![优雅登录界面](../screenshots/login_page_elegant.png)

**界面设计特色：**

🎨 **现代化设计**
- 渐变背景效果
- 毛玻璃材质设计
- 动态浮动装饰元素
- 响应式布局适配

🔐 **登录功能**
- 用户名密码验证
- 记住我选项
- 安全登录保护
- 错误提示友好

⚡ **用户体验**
- 快速加载动画
- 输入框焦点效果
- 按钮悬停动画
- 移动端优化

---

## 🏠 第二部分：主仪表盘功能

### 2.1 仪表盘概览

**主仪表盘界面：**

![主仪表盘](../screenshots/dashboard_clean.png)

**功能区域详解：**

🔝 **顶部导航栏**
- 工作台：系统首页和数据概览
- 日常管理：工作记录和检查管理
- 菜单规划：食谱和周菜单管理
- 供应链：采购、入库、出库管理
- 质量安全：食品安全和溯源管理
- 财务管理：会计科目、凭证、报表
- 系统：用户管理和系统设置

👤 **用户信息区域**
- 当前登录用户：guest_demo
- 消息通知功能
- 用户设置选项

📍 **面包屑导航**
- 首页 / 财务管理
- 清晰的页面层级关系

### 2.2 通知与待办事项

**📢 通知消息区域**
- 系统状态：一切正常，无需特别关注的事项
- 重要提醒：今日日志已存在
- 欢迎信息：欢迎使用游客体验模式

**📋 今日待办事项**
1. ✅ 完成食材入库（今日无入库任务）
2. ✅ 准备出库计划（已完成）
3. ✅ 制定明日菜单（已制定）

### 2.3 供应链状态概览

**📊 实时数据监控**

| 模块 | 状态 | 详细信息 |
|------|------|----------|
| 📋 采购订单 | 正常 | 待确认0单，已完成0单，总金额¥0 |
| 📦 食材入库 | 正常 | 今日入库0批次，待检验0批次，合格率0% |
| 📊 消耗计划 | 待制定 | 今日计划未制定，执行率0%，预计消耗¥0 |
| 🚛 食材出库 | 正常 | 今日出库0批次，待出库0批次，库存周转正常 |

### 2.4 食堂管理流程

**🔄 完整工作流程（当前进度37%）**

1. **📅 周菜单计划** - 待制定
   - 查看现有食谱
   - 新建周菜单
   - 操作指引

2. **🛒 采购计划** - 已完成(8/8)（已逾期1天）
   - 查看采购订单
   - 新建采购单
   - 操作指引

3. **📦 入库管理** - 进行中(2/5)（还剩1天）
   - 查看入库记录
   - 新建入库单
   - 操作指引

4. **📊 消耗量计划** - 未开始(0/5)（还剩2天）
   - 查看消耗计划
   - 新建消耗计划
   - 操作指引

5. **🚛 出库管理** - 未开始(0/5)（还剩3天）
   - 查看出库记录
   - 新建出库单
   - 操作指引

6. **📋 库存管理** - 需注意
   - 查看库存状态
   - 库存盘点
   - 操作指引

7. **🥄 留样记录** - 已完成(3/3)（已逾期1天）
   - 查看留样记录
   - 新建留样记录
   - 操作指引

8. **🔍 溯源管理** - 未开始(0/0)
   - 查看溯源信息
   - 搜索溯源记录
   - 操作指引

### 2.5 日常管理功能

**📝 核心功能模块**
- 工作日志：记录日常工作内容
- 检查记录：食品安全检查（支持扫码上传）
- 陪餐记录：领导陪餐情况记录
- 培训记录：员工培训档案管理
- 特殊事件：突发事件记录处理
- 问题记录：问题跟踪和解决

### 2.6 今日菜单展示

**🍽️ 三餐菜品安排**

**🌅 早餐（共5道菜品）**
1. 🏫 剁椒笋片
2. 🏫 包子
3. 🏫 熟鸡蛋
4. 🏫 爽口面条
5. 🏫 红薯米饭

**🌞 午餐（共4道菜品）**
1. 🏫 红薯米饭
2. 🏫 韭菜炒豆芽
3. 🏫 蒸蛋羹
4. 🏫 熟鸡蛋

**🌙 晚餐（共5道菜品）**
1. 🏫 红薯米饭
2. 🏫 蒸蛋羹
3. 🏫 炒包菜
4. 🏫 韭菜炒豆芽
5. 🏫 西红柿炒蛋

---

## 💰 第三部分：财务管理模块

### 3.1 财务菜单展开

**财务管理菜单结构：**

![财务管理菜单](../screenshots/financial_menu_expanded.png)

**📊 完整功能模块**
- 财务概览：财务数据总览和关键指标
- 会计科目：系统科目和学校科目管理
- 财务凭证：凭证录入、审核和管理
- 应付账款：供应商应付款管理
- 付款记录：付款流水和统计
- 待处理入库：入库财务处理
- 明细账查询：科目明细查询
- 总账查询：总账数据查询
- 科目余额表：科目余额统计
- 资产负债表：财务状况报表
- 成本分析表：成本结构分析
- 账龄分析：应付款账龄分析

### 3.2 财务凭证管理

**财务凭证管理界面：**

![财务凭证管理页面](../screenshots/financial_vouchers_page.png)

**📋 页面功能区域详解**

**🔝 统计信息栏**
- 总计：6张凭证
- 待审核：0张凭证
- 已审核：6张凭证
- 本月金额：¥142,748.20

**🛠️ 操作工具栏**
- ➕ 新建：创建新的财务凭证
- 🔮 自动生成：从业务单据自动生成凭证
- 📝 批量记账：批量处理凭证记账
- ❌ 取消审核：取消已审核凭证
- 🖨️ 批量打印：批量打印凭证
- 📤 批量导出：批量导出凭证数据
- 📤 导出：导出当前查询结果
- ❓ 帮助：操作帮助文档

**🔍 查询筛选区域**
- 搜索：支持凭证号/摘要搜索
- 类型：全部/收款/付款/转账/记账
- 状态：全部/草稿/待审核/已审核/已记账
- 日期范围：开始日期到结束日期
- 🔍 查询：执行查询操作
- 🔄 重置：重置查询条件

### 3.3 凭证详情展示

**📝 凭证记录详解**

**凭证1：入库业务凭证**
- 月份：06月
- 制单人：hqzcdyxx461
- 凭证字号：2
- 附单据：0张
- 状态：已审核

**会计分录：**
| 摘要 | 科目 | 借方金额 | 贷方金额 |
|------|------|----------|----------|
| 2025年06月14日从绿色农场有限公司购买其他：面粉(100公斤)、大米(108.3公斤)、面条(47.0公斤) | 1201 原材料 | 1,948.40 | |
| 2025年06月14日从绿色农场有限公司购买肉类：猪肉(100公斤)、鸡蛋(100公斤) | 120102 肉类 | 8,300.00 | |
| 2025年06月14日应付绿色农场有限公司货款-入库单RK20250614230014 | 2001 应付账款 | | 10,248.40 |
| **合计** | | **10,248.40** | **10,248.40** |

**🔧 凭证操作功能**
- ✏️ 编辑：修改凭证内容
- 👁️ 查看：查看凭证详情
- 🖨️ 打印：打印凭证
- 📤 导出：导出凭证
- ✅ 审核：审核凭证
- ❌ 取消审核：取消审核状态

---

## 📱 第四部分：移动端功能

### 4.1 移动端特色

**📱 移动端核心功能**
- 📷 二维码扫描：快速录入检查信息
- 📸 现场拍照：实时图片上传记录
- 📊 实时查询：随时查看库存和菜单
- 📢 消息推送：重要提醒及时通知
- 🔄 离线同步：网络恢复后自动同步

### 4.2 操作便捷性

**🎯 移动端优势**
- 响应式设计，完美适配各种设备
- 触摸友好的界面设计
- 快速加载，流畅操作体验
- 支持手势操作和语音输入

---

## 🎯 第五部分：最佳实践建议

### 5.1 日常操作流程

**🌅 晨间准备（8:00-9:00）**
1. 登录系统查看今日待办
2. 检查库存预警信息
3. 确认当日菜单安排
4. 查看供应商配送计划

**🛒 采购管理（9:00-10:00）**
1. 根据周菜单生成采购需求
2. 联系供应商确认配送时间
3. 录入采购订单信息
4. 跟踪订单执行状态

**📦 入库检查（10:00-12:00）**
1. 食材到货验收登记
2. 质量检查和记录
3. 入库操作和标签
4. 更新库存数据

**🍳 制作准备（12:00-17:00）**
1. 按菜单需求出库食材
2. 记录实际消耗量
3. 更新库存状态
4. 准备制作工具

**🍽️ 用餐服务（17:00-19:00）**
1. 食品留样操作
2. 陪餐人员记录
3. 用餐问题记录
4. 清洁卫生检查

**📝 日终总结（19:00-20:00）**
1. 工作日志记录
2. 数据统计分析
3. 问题汇总处理
4. 明日计划制定

### 5.2 财务管理建议

**📅 每日操作**
- 及时录入当日财务凭证
- 检查应付款到期提醒
- 处理待审核凭证
- 更新付款记录

**📊 每周操作**
- 生成周财务报表
- 核对银行账户余额
- 分析成本变化趋势
- 处理供应商对账

**📈 每月操作**
- 生成月度财务报表
- 进行账龄分析
- 核对科目余额
- 财务数据备份

---

## 📞 第六部分：技术支持

### 6.1 联系方式

**📞 技术支持热线**
- 电话：18373062333
- 时间：工作日 9:00-18:00
- 服务：专业技术团队解答

**📧 邮件支持**
- 邮箱：<EMAIL>
- 响应：24小时内回复
- 内容：详细问题描述和解决方案

**💬 在线客服**
- 时间：工作日 9:00-18:00
- 特点：即时响应，快速解决

### 6.2 常见问题

**🔐 登录问题**
- 检查用户名密码是否正确
- 清除浏览器缓存重试
- 联系管理员重置密码

**🔄 数据同步问题**
- 刷新页面或重新登录
- 检查网络连接状态
- 等待系统自动同步

**📷 图片上传问题**
- 检查网络连接稳定性
- 确认图片格式和大小
- 尝试压缩图片后上传

**🔑 权限问题**
- 联系系统管理员
- 确认角色权限配置
- 申请相应功能权限

---

## 📸 第七部分：配套截图资源

### 🖼️ 截图文件列表

| 截图文件名 | 页面内容 | 用途 |
|------------|----------|------|
| `homepage_overview.png` | 系统首页 | 展示系统整体介绍和功能概览 |
| `login_page_elegant.png` | 优雅登录页面 | 展示新版登录界面设计 |
| `dashboard_clean.png` | 主仪表盘 | 展示系统核心控制中心 |
| `financial_menu_expanded.png` | 财务管理菜单 | 展示财务模块功能架构 |
| `financial_vouchers_page.png` | 财务凭证管理页面 | 展示专业财务凭证功能 |
| `supply_chain_menu_expanded.png` | 供应链管理菜单 | 展示供应链模块功能架构 |
| `supplier_management_page.png` | 供应商管理页面 | 展示供应商档案管理功能 |
| `stock_in_management_page.png` | 入库管理页面 | 展示食材入库流程管理 |
| `consumption_plan_management_page.png` | 消耗计划管理页面 | 展示食材需求规划功能 |
| `stock_out_management_page.png` | 出库管理页面 | 展示食材出库流程管理 |
| `menu_planning_expanded.png` | 菜单规划菜单 | 展示菜单规划模块功能架构 |
| `ingredient_management_page.png` | 食材管理页面 | 展示食材档案管理功能 |
| `recipe_library_page.png` | 食谱库页面 | 展示食谱管理和配方功能 |

### 📋 截图使用说明

1. **文件格式：** PNG格式，高清无损
2. **分辨率：** 1920x1080或更高
3. **存储位置：** `docs/screenshots/` 目录
4. **命名规范：** 使用英文下划线命名，便于引用
5. **更新频率：** 随系统界面更新及时更新截图

### 🎯 截图内容说明

**系统首页截图**
- 展示完整的首页布局和功能介绍
- 包含导航栏、功能模块、联系方式等关键信息
- 体现系统的专业性和易用性

**登录界面截图**
- 展示优雅的登录页面设计
- 包含渐变背景、毛玻璃效果等现代化元素
- 体现系统的视觉设计水平

**主仪表盘截图**
- 展示系统的核心控制中心
- 包含通知消息、待办事项、供应链状态等关键信息
- 体现系统的数据整合能力

**各模块功能截图**
- 展示各个功能模块的详细界面
- 包含数据列表、操作按钮、筛选功能等
- 体现系统的功能完整性和专业性

---

**💡 提示：本指南包含真实系统截图，确保操作指导的准确性。如有疑问请联系技术支持团队。**

**🌐 官方网址：xiaoyuanst.com**

**📱 24小时在线，一键管理，专业服务校园餐全场景**
