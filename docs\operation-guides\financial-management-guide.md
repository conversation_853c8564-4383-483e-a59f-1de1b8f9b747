# 财务管理模块操作指南

## 📊 模块概述

财务管理模块是智慧食堂平台的核心功能之一，提供专业的财务管理解决方案，包含会计科目管理、财务凭证创建、资产负债表和利润表等财务报表，为学校食堂提供完整的财务核算体系。

## 🎯 核心功能

### 💼 主要功能模块

**📋 基础设置**
- 会计科目管理（系统科目 + 学校科目）
- 财务参数配置
- 用户权限设置

**📝 日常业务**
- 财务凭证录入
- 凭证审核流程
- 应付账款管理
- 付款记录维护

**📊 查询统计**
- 明细账查询
- 总账查询
- 科目余额表
- 待处理入库

**📈 财务报表**
- 资产负债表
- 成本分析表
- 账龄分析报告
- 财务概览

## 🚀 快速开始

### 1️⃣ 访问财务模块

**操作步骤：**
1. 登录智慧食堂平台
2. 点击顶部导航栏"💰 财务管理"
3. 选择需要的功能模块

**菜单结构：**
```
💰 财务管理
├── 📊 财务概览
├── 📋 会计科目
├── 📝 财务凭证
├── 💳 应付账款
├── 💰 付款记录
├── 📦 待处理入库
├── 📊 明细账查询
├── 📊 总账查询
├── 📊 科目余额表
├── 📈 资产负债表
├── 📈 成本分析表
└── 📈 账龄分析
```

## 📋 会计科目管理

### 🏗️ 科目体系结构

**系统科目 vs 学校科目**

| 特性 | 系统科目 | 学校科目 |
|------|----------|----------|
| 📚 **性质** | 标准化会计科目 | 学校专属科目 |
| 🌐 **范围** | 全局共享 | 学校独有 |
| 📖 **标准** | 符合会计准则 | 基于系统科目 |
| ✏️ **编辑** | 不可修改 | 可自定义修改 |
| 🎯 **用途** | 规范化管理 | 灵活适应需求 |

### 🛠️ 操作指南

**📝 创建学校科目**
1. 进入"会计科目"页面
2. 点击"新增科目"按钮
3. 选择上级科目（基于系统科目）
4. 填写科目编码和名称
5. 设置科目属性和级别
6. 保存并生效

**✏️ 编辑科目信息**
1. 在科目列表中找到目标科目
2. 点击"编辑"按钮
3. 修改科目名称或属性
4. 确认保存更改

**🗑️ 删除科目**
- ⚠️ 注意：只能删除未使用的学校科目
- 系统科目不允许删除
- 已有业务数据的科目不能删除

## 📝 财务凭证管理

### 🎯 凭证类型

**📋 主要凭证类型：**
- 💰 **收款凭证**：现金、银行存款收入
- 💸 **付款凭证**：现金、银行存款支出  
- 🔄 **转账凭证**：不涉及现金银行的业务
- 📊 **记账凭证**：通用记账凭证

### 🛠️ 凭证操作流程

**📝 创建凭证**
1. 点击"财务凭证" → "新增凭证"
2. 选择凭证类型和日期
3. 填写凭证摘要
4. 添加会计分录：
   - 选择会计科目
   - 输入借方/贷方金额
   - 填写明细说明
5. 检查借贷平衡
6. 保存凭证

**✅ 凭证审核**
1. 在凭证列表中选择待审核凭证
2. 点击"审核"按钮
3. 检查凭证内容和金额
4. 确认审核通过或退回修改

**🔍 凭证查询**
- 按日期范围查询
- 按凭证号查询
- 按科目查询
- 按审核状态查询

### ⚠️ 常见问题

**❓ 问题：添加明细时会计科目下拉框为空**
**💡 解决：需要先初始化会计科目，参考会计科目管理帮助**

**❓ 问题：凭证借贷不平衡**
**💡 解决：检查所有分录的借方和贷方金额，确保借方总额=贷方总额**

## 💳 应付账款管理

### 🎯 功能概述

应付账款管理帮助学校跟踪和管理对供应商的应付款项，确保资金流的透明和可控。

### 📊 主要功能

**📝 应付登记**
- 根据采购订单自动生成应付款
- 手动录入应付款项
- 应付款项分类管理

**💰 付款管理**
- 付款计划制定
- 付款执行记录
- 付款状态跟踪

**📈 账龄分析**
- 应付款账龄统计
- 逾期款项提醒
- 供应商信用分析

**🤝 供应商对账**
- 与供应商核对账目
- 差异分析和处理
- 对账单生成

### 🛠️ 操作流程

**📝 登记应付款**
1. 进入"应付账款"页面
2. 点击"新增应付款"
3. 选择供应商
4. 填写应付金额和日期
5. 选择对应的采购订单
6. 保存应付记录

**💰 处理付款**
1. 在应付款列表中选择待付款项
2. 点击"付款"按钮
3. 选择付款方式和账户
4. 填写实际付款金额
5. 生成付款凭证
6. 更新应付款状态

## 📊 财务报表

### 📈 资产负债表

**📋 报表内容：**
- 💰 **资产部分**：流动资产、固定资产
- 💳 **负债部分**：流动负债、长期负债
- 🏛️ **所有者权益**：实收资本、留存收益

**🛠️ 生成步骤：**
1. 进入"资产负债表"页面
2. 选择报表日期
3. 点击"生成报表"
4. 查看报表数据
5. 导出PDF或Excel

### 📊 成本分析表

**📋 分析维度：**
- 🥬 **食材成本**：按类别统计
- 👥 **人工成本**：工资福利
- ⚡ **运营成本**：水电气等
- 📊 **成本占比**：各项成本比例

### 📈 账龄分析

**📊 分析内容：**
- 📅 **账龄分布**：30天、60天、90天以上
- ⚠️ **逾期提醒**：超期应付款提醒
- 📈 **趋势分析**：应付款变化趋势
- 🏢 **供应商分析**：各供应商应付情况

## 🔍 账务查询

### 📊 明细账查询

**🎯 查询功能：**
- 按科目查询明细
- 按时间范围筛选
- 按凭证号查询
- 按摘要关键词搜索

### 📋 总账查询

**📊 总账内容：**
- 科目期初余额
- 本期借方发生额
- 本期贷方发生额
- 期末余额

### 📈 科目余额表

**📊 余额信息：**
- 各级科目余额
- 借贷方向显示
- 余额变动分析
- 科目汇总统计

## 🎯 最佳实践

### ✅ 日常操作建议

**📅 每日操作：**
1. 及时录入当日财务凭证
2. 检查应付款到期提醒
3. 处理待审核凭证
4. 更新付款记录

**📊 每周操作：**
1. 生成周财务报表
2. 核对银行账户余额
3. 分析成本变化趋势
4. 处理供应商对账

**📈 每月操作：**
1. 生成月度财务报表
2. 进行账龄分析
3. 核对科目余额
4. 财务数据备份

### ⚠️ 注意事项

**🔒 权限管理：**
- 严格控制财务数据访问权限
- 定期更新用户权限设置
- 重要操作需要审核确认

**💾 数据安全：**
- 定期备份财务数据
- 重要凭证保留纸质档案
- 建立数据恢复机制

**📋 规范操作：**
- 遵循会计准则和制度
- 保持凭证录入的及时性
- 确保数据的准确性和完整性

## 📞 技术支持

**🆘 遇到问题时：**
1. 查看系统帮助文档
2. 联系技术支持热线：18373062333
3. 发送邮件至：<EMAIL>
4. 使用在线客服功能

**📚 学习资源：**
- 系统操作视频教程
- 财务管理最佳实践指南
- 常见问题解答（FAQ）
- 用户操作手册

---

**💡 提示：财务管理涉及学校重要资金信息，请严格按照财务制度和操作规范使用系统功能。**
