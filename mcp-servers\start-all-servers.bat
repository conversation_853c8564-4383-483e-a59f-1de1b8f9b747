@echo off
echo 启动StudentsCMSSP开发辅助MCP服务器...

echo.
echo 1. 启动SQL Server MCP服务器...
start "SQL Server MCP" cmd /k "cd /d %~dp0\..\mcp-sqlserver && node server.js"

echo.
echo 2. 启动Filesystem MCP服务器...
start "Filesystem MCP" cmd /k "cd /d %~dp0\filesystem-mcp && node server.js"

echo.
echo 3. 启动Excel MCP服务器...
start "Excel MCP" cmd /k "cd /d %~dp0\excel-mcp && node server.js"

echo.
echo 4. 启动Pandoc MCP服务器...
start "Pandoc MCP" cmd /k "cd /d %~dp0\pandoc-mcp && node server.js"

echo.
echo 所有MCP服务器已启动！
echo.
echo 可用的MCP服务器：
echo - SQL Server MCP: 数据库查询和管理
echo - Filesystem MCP: 文件系统操作
echo - Excel MCP: Excel文件处理
echo - Pandoc MCP: 文档格式转换
echo.
pause
