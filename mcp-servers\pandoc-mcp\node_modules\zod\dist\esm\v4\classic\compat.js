// Zod 3 compat layer
import * as core from "zod/v4/core";
/** @deprecated Use the raw string literal codes instead, e.g. "invalid_type". */
export const ZodIssueCode = {
    invalid_type: "invalid_type",
    too_big: "too_big",
    too_small: "too_small",
    invalid_format: "invalid_format",
    not_multiple_of: "not_multiple_of",
    unrecognized_keys: "unrecognized_keys",
    invalid_union: "invalid_union",
    invalid_key: "invalid_key",
    invalid_element: "invalid_element",
    invalid_value: "invalid_value",
    custom: "custom",
};
/** @deprecated Not necessary in Zod 4. */
const INVALID = Object.freeze({
    status: "aborted",
});
/** A special constant with type `never` */
export const NEVER = INVALID;
export { $brand, config } from "zod/v4/core";
/** @deprecated Use `z.config(params)` instead. */
export function setErrorMap(map) {
    core.config({
        customError: map,
    });
}
/** @deprecated Use `z.config()` instead. */
export function getErrorMap() {
    return core.config().customError;
}
