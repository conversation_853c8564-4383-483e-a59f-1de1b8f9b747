# 智慧食堂平台视频录制脚本库

## 📖 脚本库概述

本脚本库为智慧食堂平台提供了完整的视频录制解说词和操作指导，涵盖系统的所有核心功能模块。每个脚本都包含详细的解说词、操作步骤、录制要点和技术要求，确保录制出专业、实用的产品演示视频。

---

## 📁 脚本文件列表

### 🎬 1. 完整系统演示脚本
**文件名：** `complete-system-demo.md`  
**时长：** 15-20分钟  
**目标观众：** 学校管理员、食堂管理人员、财务人员  
**内容概述：** 从登录到各模块的全面演示，适合初次了解系统的用户

**主要章节：**
- 系统介绍与登录（0:00-3:00）
- 主仪表盘功能介绍（3:00-6:00）
- 日常管理功能演示（6:00-9:00）
- 财务管理模块深度演示（9:00-15:00）
- 系统特色与优势总结（15:00-18:00）
- 结束语与行动号召（18:00-20:00）

### 💰 2. 财务管理专项演示脚本
**文件名：** `financial-management-demo.md`  
**时长：** 10-12分钟  
**目标观众：** 学校财务人员、会计人员、财务主管  
**内容概述：** 深度展示财务管理模块的专业功能

**主要章节：**
- 财务模块概述（0:00-2:00）
- 会计科目管理（2:00-4:00）
- 财务凭证管理（4:00-7:00）
- 应付账款管理（7:00-9:00）
- 财务报表与查询（9:00-11:00）
- 系统优势与总结（11:00-12:00）

### 🚚 3. 供应链管理演示脚本
**文件名：** `supply-chain-demo.md`
**时长：** 8-10分钟
**目标观众：** 食堂管理人员、采购人员、库管人员
**内容概述：** 全面展示从采购到出库的全流程管理

**主要章节：**
- 供应链模块概述（0:00-1:30）
- 采购管理演示（1:30-3:30）
- 入库管理演示（3:30-5:30）
- 库存管理演示（5:30-7:00）
- 出库管理演示（7:00-8:30）
- 系统优势与总结（8:30-10:00）

### 🏢 4. 供应商管理专项演示脚本
**文件名：** `01-supplier-management-demo.md`
**时长：** 8-10分钟
**目标观众：** 采购人员、供应链管理人员、食堂管理员
**内容概述：** 详细展示供应商档案管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 供应商管理界面详解（1:30-3:30）
- 供应商档案详细信息分析（3:30-6:00）
- 供应商管理操作演示（6:00-8:00）
- 系统特色与优势总结（8:00-10:00）

### 📦 5. 入库管理专项演示脚本
**文件名：** `02-stock-in-management-demo.md`
**时长：** 10-12分钟
**目标观众：** 库管人员、验收人员、采购人员、食堂管理员
**内容概述：** 详细展示入库管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 入库管理界面详解（1:30-3:30）
- 入库单详细信息分析（3:30-6:00）
- 入库管理操作演示（6:00-8:30）
- 质量管理与追溯（8:30-10:30）
- 系统特色与优势总结（10:30-12:00）

### 📊 6. 消耗计划管理专项演示脚本
**文件名：** `03-consumption-plan-demo.md`
**时长：** 8-10分钟
**目标观众：** 厨师长、食堂管理员、营养师、库管人员
**内容概述：** 详细展示消耗计划管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 消耗计划界面详解（1:30-3:30）
- 消耗计划详细信息分析（3:30-5:30）
- 消耗计划创建方式演示（5:30-7:30）
- 系统特色与优势总结（7:30-10:00）

### 🚛 7. 出库管理专项演示脚本
**文件名：** `04-stock-out-management-demo.md`
**时长：** 10-12分钟
**目标观众：** 库管人员、厨师长、食堂管理员、营养师
**内容概述：** 详细展示出库管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 出库管理界面详解（1:30-4:00）
- 出库单筛选与查询功能（4:00-6:00）
- 出库单详细信息分析（6:00-8:00）
- 出库流程与操作演示（8:00-10:00）
- 系统特色与优势总结（10:00-12:00）

### 🥬 8. 食材管理专项演示脚本
**文件名：** `05-ingredient-management-demo.md`
**时长：** 8-10分钟
**目标观众：** 营养师、厨师长、食堂管理员、采购人员
**内容概述：** 详细展示食材档案管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 食材管理界面详解（1:30-3:30）
- 食材档案详细信息分析（3:30-5:30）
- 食材管理操作演示（5:30-7:30）
- 系统特色与优势总结（7:30-10:00）

### 🍳 9. 食谱库管理专项演示脚本
**文件名：** `06-recipe-library-demo.md`
**时长：** 10-12分钟
**目标观众：** 厨师长、营养师、食堂管理员、菜单规划人员
**内容概述：** 详细展示食谱配方管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 食谱库界面详解（1:30-3:30）
- 食谱详细信息分析（3:30-6:00）
- 食谱管理操作演示（6:00-8:30）
- 系统特色与优势总结（8:30-12:00）

### 📅 10. 周菜单计划专项演示脚本
**文件名：** `07-weekly-menu-plan-demo.md`
**时长：** 10-12分钟
**目标观众：** 营养师、厨师长、食堂管理员、菜单规划人员
**内容概述：** 详细展示科学膳食规划管理的完整功能

**主要章节：**
- 模块介绍与登录（0:00-1:30）
- 周菜单界面详解（1:30-4:00）
- 菜单内容详细分析（4:00-7:00）
- 菜单规划特色功能（7:00-9:00）
- 系统特色与优势总结（9:00-12:00）

---

## 📸 配套截图资源

### 🖼️ 截图文件列表

| 截图文件名 | 页面内容 | 用途 |
|------------|----------|------|
| `homepage_overview.png` | 系统首页 | 展示系统整体介绍和功能概览 |
| `login_page_elegant.png` | 优雅登录页面 | 展示新版登录界面设计 |
| `dashboard_clean.png` | 主仪表盘 | 展示系统核心控制中心 |
| `financial_menu_expanded.png` | 财务管理菜单 | 展示财务模块功能架构 |
| `financial_vouchers_page.png` | 财务凭证管理页面 | 展示专业财务凭证功能 |
| `supply_chain_menu_expanded.png` | 供应链管理菜单 | 展示供应链模块功能架构 |
| `supplier_management_page.png` | 供应商管理页面 | 展示供应商档案管理功能 |
| `stock_in_management_page.png` | 入库管理页面 | 展示食材入库流程管理 |
| `consumption_plan_management_page.png` | 消耗计划管理页面 | 展示食材需求规划功能 |
| `stock_out_management_page.png` | 出库管理页面 | 展示食材出库流程管理 |
| `menu_planning_expanded.png` | 菜单规划菜单 | 展示菜单规划模块功能架构 |
| `ingredient_management_page.png` | 食材管理页面 | 展示食材档案管理功能 |
| `recipe_library_page.png` | 食谱库页面 | 展示食谱管理和配方功能 |
| `weekly_menu_plan_page.png` | 周菜单计划页面 | 展示科学膳食规划管理功能 |

### 📋 截图使用说明

1. **文件格式：** PNG格式，高清无损
2. **分辨率：** 1920x1080或更高
3. **存储位置：** `docs/screenshots/` 目录
4. **命名规范：** 使用英文下划线命名，便于引用
5. **更新频率：** 随系统界面更新及时更新截图

---

## 🎯 录制指导原则

### 📝 内容准备

**1. 脚本熟悉**
- 录制前充分熟悉解说词
- 理解每个功能点的业务价值
- 准备可能的问答和补充说明

**2. 环境准备**
- 使用游客账号登录 xiaoyuanst.com
- 确保网络连接稳定
- 准备干净的浏览器环境

**3. 数据准备**
- 使用系统中的真实演示数据
- 确保数据的完整性和代表性
- 避免使用敏感或不当的数据

### 🎥 录制技术要求

**视频设置**
- **分辨率：** 1920x1080 (Full HD)
- **帧率：** 30fps
- **格式：** MP4 (H.264编码)
- **码率：** 5-8 Mbps

**音频设置**
- **采样率：** 48kHz
- **声道：** 立体声
- **格式：** AAC编码
- **音质：** 清晰无杂音，音量适中

**录制环境**
- **浏览器：** Chrome或Edge最新版本
- **屏幕：** 干净的桌面背景
- **光标：** 突出显示，便于跟踪
- **录制软件：** OBS Studio、Camtasia等专业软件

### 🎤 解说技巧

**语言表达**
- **语速：** 适中，便于理解（每分钟150-180字）
- **语调：** 专业、友好、自信
- **发音：** 清晰准确，避免方言
- **停顿：** 适当停顿，给观众思考时间

**内容组织**
- **逻辑清晰：** 按照业务流程顺序讲解
- **重点突出：** 强调核心功能和价值
- **举例说明：** 使用具体例子帮助理解
- **总结回顾：** 每个章节结束时简要总结

### 📱 操作演示

**鼠标操作**
- **移动缓慢：** 便于观众跟踪
- **点击明确：** 确保点击动作清晰可见
- **悬停提示：** 适当悬停展示功能提示
- **滚动平滑：** 页面滚动要平滑自然

**页面展示**
- **停留时间：** 每个重要页面停留足够时间
- **区域指示：** 用鼠标圈选重要区域
- **功能演示：** 确保每个功能都有实际操作
- **结果展示：** 展示操作后的结果和效果

---

## 🎬 后期制作指南

### ✂️ 剪辑要求

**基本剪辑**
- **去除冗余：** 删除不必要的停顿和重复
- **节奏控制：** 保持适当的节奏感
- **转场自然：** 场景切换要平滑自然
- **时长控制：** 严格控制在预定时长内

**视觉效果**
- **片头片尾：** 添加品牌标识和联系方式
- **字幕添加：** 重要信息添加字幕说明
- **标注指示：** 重要功能添加箭头或高亮
- **画面稳定：** 确保画面稳定不抖动

### 🎨 视觉设计

**品牌元素**
- **Logo展示：** 在适当位置展示品牌Logo
- **色彩统一：** 使用品牌标准色彩
- **字体规范：** 使用统一的字体样式
- **风格一致：** 保持整体视觉风格一致

**信息展示**
- **联系方式：** 在片尾展示完整联系信息
- **网址展示：** 突出显示官方网址
- **二维码：** 可添加二维码便于移动端访问
- **版权信息：** 添加版权声明和制作信息

---

## 📊 质量控制

### ✅ 检查清单

**内容检查**
- [ ] 解说词准确无误
- [ ] 功能演示完整
- [ ] 数据展示真实
- [ ] 逻辑结构清晰

**技术检查**
- [ ] 视频清晰度达标
- [ ] 音频质量良好
- [ ] 画面稳定流畅
- [ ] 文件格式正确

**品牌检查**
- [ ] Logo展示正确
- [ ] 联系方式准确
- [ ] 品牌信息完整
- [ ] 版权声明清晰

### 📈 效果评估

**观众反馈**
- 收集观众观看反馈
- 分析观看完成率
- 统计互动数据
- 优化改进建议

**业务效果**
- 跟踪咨询转化率
- 监控试用注册量
- 分析客户来源
- 评估推广效果

---

## 📞 技术支持

### 🆘 录制支持

如果在录制过程中遇到技术问题，可以联系：

**技术支持热线：** 18373062333  
**邮件支持：** <EMAIL>  
**服务时间：** 工作日 9:00-18:00

### 📚 学习资源

**参考资料**
- 系统操作手册
- 功能说明文档
- 最佳实践指南
- 常见问题解答

**培训支持**
- 产品功能培训
- 录制技巧指导
- 后期制作支持
- 推广策略建议

---

**💡 提示：本脚本库会根据系统更新和用户反馈持续优化，确保内容的准确性和实用性。**

**🎬 让我们一起创造专业、实用、有价值的产品演示视频！**
